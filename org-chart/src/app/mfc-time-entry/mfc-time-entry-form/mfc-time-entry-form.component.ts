import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MfcTimeEntryService } from '../../services/mfc-time-entry.service';
import { MfcTimeEntry } from '../../model/mfc-time-entry.model';
import { AuthService } from 'src/app/auth.service';
import { EmployeeService } from 'src/app/employee.service';

@Component({
  selector: 'app-mfc-time-entry-form',
  templateUrl: './mfc-time-entry-form.component.html',
  styleUrls: ['./mfc-time-entry-form.component.css']
})
export class MfcTimeEntryFormComponent implements OnInit {
  form: FormGroup;
  submitting = false;
  successMsg = '';
  errorMsg = '';
  userId = '';
  resourceName = '';
  reportingSeniorUserId = '';
  company = '';

  constructor(
    private fb: FormBuilder,
    private mfcService: MfcTimeEntryService,
    private auth: AuthService,
    private employeeService: EmployeeService
  ) {
    this.form = this.fb.group({
      dateWorked: ['', Validators.required],
      hoursWorked: [null, [Validators.required, Validators.min(0.01)]],
      billableHoursWorked: [null, [Validators.required, Validators.min(0)]],
      notes: [''],
      internalNotes: ['']
    });
  }

  ngOnInit() {
    this.userId = localStorage.getItem('username') || '';
    this.employeeService.getEmployeeById(this.userId).subscribe(result => {
      const emp: any = result.data;
      this.resourceName = (emp.lastName ? emp.lastName : '') + (emp.lastName && emp.firstName ? ', ' : '') + (emp.firstName ? emp.firstName : '');
      this.reportingSeniorUserId = emp.lead || this.userId;
      this.company = emp.vendor || 'ABC';
      
    });
  }

  submit() {
    if (this.form.invalid) return;
    this.submitting = true;
    this.successMsg = '';
    this.errorMsg = '';
    const entry: Partial<MfcTimeEntry> = {
      userId: this.userId,
      reportingSeniorUserId: this.reportingSeniorUserId,
      resourceName: this.resourceName,
      company: this.company,
      type: 'Ticket',
      ...this.form.value
    };
    this.mfcService.create(entry).subscribe({
      next: () => {
        this.successMsg = 'Time entry submitted!';
        this.form.reset();
        this.submitting = false;
      },
      error: err => {
        this.errorMsg = err.error?.message || 'Submission failed.';
        this.submitting = false;
      }
    });
  }
} 