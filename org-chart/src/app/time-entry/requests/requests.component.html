<div class="requests-dashboard">
  <app-page-header
  title="Requests"
  subtitle="Manage and approve time entry requests across the organization">
</app-page-header>
  <div class="actions">
    <button mat-raised-button (click)="backToTimeEntries()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
      Back to Time Entries
    </button>

    <div class="action-buttons">
      <button mat-raised-button color="primary" (click)="approveAllSelected()" [disabled]="!selection.hasValue()">
        <mat-icon>check_circle</mat-icon>
        Approve Selected
      </button>
      <button mat-raised-button color="warn" (click)="rejectAllSelected()" [disabled]="!selection.hasValue()">
        <mat-icon>cancel</mat-icon>
        Reject Selected
      </button>
      <button mat-raised-button color="accent" class="action-button download-button" (click)="downloadExcelWithSummary()">
        <mat-icon>download</mat-icon> Download Excel
      </button>
    </div>

    <mat-form-field appearance="outline" class="filter-field">
      <mat-label>Filter by Status</mat-label>
      <mat-select (selectionChange)="applyFilter($event, 'status')">
        <mat-option value="">All</mat-option>
        <mat-option *ngFor="let status of requestStatuses" [value]="status">{{ status }}</mat-option>
      </mat-select>
    </mat-form-field>

    <div class="date-range-container">
      <mat-form-field appearance="outline" class="date-field">
        <mat-label>Start Date</mat-label>
        <input matInput [matDatepicker]="startDatePicker" [(ngModel)]="startDate">
        <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline" class="date-field">
        <mat-label>End Date</mat-label>
        <input matInput [matDatepicker]="endDatePicker" [(ngModel)]="endDate">
        <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #endDatePicker></mat-datepicker>
      </mat-form-field>

      <button mat-raised-button class="apply-button" (click)="applyDateRange()" [disabled]="!startDate || !endDate">
        <mat-icon>filter_list</mat-icon>
        Apply
      </button>
    </div>

    <!-- Show Column Filters Button -->
    <button mat-raised-button (click)="toggleColumnFilters()" class="action-button toggle-filter-button">
      <mat-icon>{{ showColumnFilters ? 'visibility_off' : 'filter_list' }}</mat-icon>
      {{ showColumnFilters ? 'Hide' : 'Show' }} Column Filters
    </button>

    <!-- Column Toggle Button -->
    <button mat-raised-button [matMenuTriggerFor]="columnMenu" class="column-toggle-button">
      <mat-icon>view_column</mat-icon>
      Toggle Columns
    </button>

    <!-- Column Toggle Menu -->
    <mat-menu #columnMenu="matMenu" class="column-menu">
      <div class="column-menu-content" (click)="$event.stopPropagation()">
        <h3 class="column-menu-title">Toggle Columns</h3>
        <mat-divider></mat-divider>

        <!-- Search input -->
        <mat-form-field appearance="outline" class="column-search-field">
          <mat-label>Search columns</mat-label>
          <input matInput [(ngModel)]="columnSearchText" placeholder="Search columns">
          <button *ngIf="columnSearchText" matSuffix mat-icon-button aria-label="Clear" (click)="columnSearchText=''">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <!-- Select All checkbox -->
        <div class="select-all-container">
          <mat-checkbox
            [checked]="allColumnsSelected"
            (change)="toggleAllColumns($event.checked)"
            class="select-all-checkbox">
            Select All
          </mat-checkbox>
        </div>

        <mat-divider></mat-divider>

        <!-- Column checkboxes -->
        <div class="column-menu-items">
          <mat-checkbox
            *ngFor="let column of getFilteredColumns()"
            [checked]="isColumnDisplayed(column)"
            (change)="toggleColumn(column)"
            [disabled]="column === 'select' || column === 'actions'"
            class="column-toggle-checkbox">
            {{ columnDisplayNames[column] }}
          </mat-checkbox>
        </div>
      </div>
    </mat-menu>

    <!-- Global Search Field -->
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>Search</mat-label>
      <mat-icon matPrefix>search</mat-icon>
      <input matInput (keyup)="applyGlobalFilter($event)" placeholder="Search requests">
    </mat-form-field>

  </div>

  <div class="table-responsive">
    <mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
      <!-- Checkbox Column -->
      <ng-container matColumnDef="select">
        <mat-header-cell *matHeaderCellDef>
          <mat-checkbox (change)="$event ? toggleAllRows() : null"
                       [checked]="selection.hasValue() && isAllSelected()"
                       [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </mat-header-cell>
        <mat-cell *matCellDef="let row">
          <mat-checkbox (click)="$event.stopPropagation()"
                       (change)="$event ? selection.toggle(row) : null"
                       [checked]="selection.isSelected(row)">
          </mat-checkbox>
        </mat-cell>
      </ng-container>

      <!-- ID Column -->
      <ng-container matColumnDef="id">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">ID</span>
            <button mat-icon-button #idTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('id', idTrigger)"
                    [color]="isFilterActive('id') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.id }}</mat-cell>
      </ng-container>

      <!-- User ID Column -->
      <ng-container matColumnDef="userIdField">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">User ID</span>
            <button mat-icon-button #userIdFieldTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('userIdField', userIdFieldTrigger)"
                    [color]="isFilterActive('userIdField') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.userIdField || request.userName || request.username || request.ldap }}</mat-cell>
      </ng-container>

      <!-- Reporting Senior's UserID Column -->
      <ng-container matColumnDef="reportingSeniorUserId">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Reporting Senior's UserID</span>
            <button mat-icon-button #reportingSeniorUserIdTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('reportingSeniorUserId', reportingSeniorUserIdTrigger)"
                    [color]="isFilterActive('reportingSeniorUserId') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.reportingSeniorUserId || request.leadUsername }}</mat-cell>
      </ng-container>

      <!-- Resource Name Column -->
      <ng-container matColumnDef="resourceName">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Resource Name</span>
            <button mat-icon-button #resourceNameTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('resourceName', resourceNameTrigger)"
                    [color]="isFilterActive('resourceName') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.resourceName || '' }}</mat-cell>
      </ng-container>

      <!-- Company Column -->
      <ng-container matColumnDef="company">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Company</span>
            <button mat-icon-button #companyTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('company', companyTrigger)"
                    [color]="isFilterActive('company') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.company || 'ABC' }}</mat-cell>
      </ng-container>

      <!-- Date Worked Column -->
      <ng-container matColumnDef="dateWorked">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Date Worked</span>
            <button mat-icon-button #dateWorkedTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('dateWorked', dateWorkedTrigger)"
                    [color]="isFilterActive('dateWorked') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">
          {{ request.dateWorked || request.date || request.entryDate | date: 'yyyy-MM-dd' }}
        </mat-cell>
      </ng-container>

      <!-- Hours Worked Column -->
      <ng-container matColumnDef="hoursWorked">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Hours Worked</span>
            <button mat-icon-button #hoursWorkedTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('hoursWorked', hoursWorkedTrigger)"
                    [color]="isFilterActive('hoursWorked') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.hoursWorked || (request.timeInMins ? (request.timeInMins / 60).toFixed(2) : '0') }}</mat-cell>
      </ng-container>

      <!-- Billable Hours Worked Column -->
      <ng-container matColumnDef="billableHoursWorked">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Billable Hours Worked</span>
            <button mat-icon-button #billableHoursWorkedTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('billableHoursWorked', billableHoursWorkedTrigger)"
                    [color]="isFilterActive('billableHoursWorked') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.billableHoursWorked || (request.timeInMins ? (request.timeInMins / 60).toFixed(2) : '0') }}</mat-cell>
      </ng-container>

      <!-- Type Column -->
      <ng-container matColumnDef="type">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Type</span>
            <button mat-icon-button #typeTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('type', typeTrigger)"
                    [color]="isFilterActive('type') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.type || request.activity || 'Ticket' }}</mat-cell>
      </ng-container>

      <!-- Notes Column -->
      <ng-container matColumnDef="notes">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Notes</span>
            <button mat-icon-button #notesTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('notes', notesTrigger)"
                    [color]="isFilterActive('notes') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.notes || request.comment || request.comments || '-' }}</mat-cell>
      </ng-container>



      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Status</span>
            <button mat-icon-button #statusTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('status', statusTrigger)"
                    [color]="isFilterActive('status') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">
          <span [class]="'status-' + request.status?.toLowerCase()">
            {{ request.status }}
            <mat-icon *ngIf="request.status === 'APPROVED'" class="verified-icon">verified</mat-icon>
          </span>
        </mat-cell>
      </ng-container>

      <!-- Comments Column -->
      <ng-container matColumnDef="comments">
        <mat-header-cell *matHeaderCellDef mat-sort-header>
          <div class="header-container">
            <span class="header-text">Comments</span>
            <button mat-icon-button #commentsTrigger="matMenuTrigger"
                    *ngIf="showColumnFilters"
                    [matMenuTriggerFor]="filterMenu"
                    (menuOpened)="openFilterMenu('comments', commentsTrigger)"
                    [color]="isFilterActive('comments') ? 'accent' : ''"
                    (click)="$event.stopPropagation()">
              <mat-icon>filter_list</mat-icon>
            </button>
          </div>
        </mat-header-cell>
        <mat-cell *matCellDef="let request">{{ request.comment || '-' }}</mat-cell>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <mat-header-cell *matHeaderCellDef>
          <div class="header-container">
            <span class="header-text">Actions</span>
          </div>

        </mat-header-cell>
        <mat-cell *matCellDef="let request">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions menu">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="approveRequest(request)"
                    [disabled]="request.status !== 'PENDING'">
              <mat-icon color="primary">check_circle</mat-icon>
              <span>Approve</span>
            </button>
            <button mat-menu-item (click)="rejectRequest(request)"
                    [disabled]="request.status !== 'PENDING'">
              <mat-icon color="warn">cancel</mat-icon>
              <span>Reject</span>
            </button>
            <button mat-menu-item (click)="editTimeEntry(request)">
              <mat-icon color="primary">edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="deleteTimeEntry(request)">
              <mat-icon color="warn">delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
    </mat-table>

    <mat-paginator
      [length]="dataSource.data.length"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 25, 50]"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>

<!-- Filter Menu Template -->
<mat-menu #filterMenu="matMenu" class="filter-menu">
  <div class="filter-menu-content" (click)="$event.stopPropagation()">
    <ng-container *ngIf="currentFilterMenuState.columnKey">
      <!-- Search Input -->
      <mat-form-field appearance="outline" class="filter-search">
        <mat-label>Search</mat-label>
        <input matInput [(ngModel)]="currentFilterMenuState.searchText" placeholder="Search options">
      </mat-form-field>

      <!-- Select All Checkbox -->
      <mat-checkbox
        [checked]="isAllTempSelected()"
        [indeterminate]="isSomeTempSelected()"
        (change)="toggleSelectAllTemp($event.checked)">
        Select All ({{ getUniqueColumnValues(currentFilterMenuState.columnKey).length }} items)
      </mat-checkbox>
      <hr>

      <!-- Filter Options -->
      <div style="max-height: 200px; overflow-y: auto;">
        <mat-checkbox *ngFor="let value of filteredMenuOptions"
          [checked]="isTempSelected(value)"
          (change)="toggleTempSelection(value, $event.checked)">
          {{ value }}
        </mat-checkbox>
      </div>
      <hr>

      <!-- Action Buttons -->
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <button mat-button (click)="onFilterApplied()">Apply</button>
        <button mat-button (click)="clearColumnFilter()">Clear</button>
      </div>
    </ng-container>
  </div>
</mat-menu>
