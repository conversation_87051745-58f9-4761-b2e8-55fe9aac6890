<div class="week-copy-dialog-container">
  <h2 mat-dialog-title>Copy Time Entry to Week</h2>

  <div class="source-entry-info">
    <h3>Source Entry</h3>
    <div class="info-row">
      <span class="label">Date:</span>
      <span class="value">{{ sourceEntry.entryDate || sourceEntry.date }}</span>
    </div>
    <div class="info-row">
      <span class="label">Project:</span>
      <span class="value">{{ sourceEntry.projectName }}</span>
    </div>
    <div class="info-row">
      <span class="label">Process:</span>
      <span class="value">{{ sourceEntry.process }}</span>
    </div>
    <div class="info-row">
      <span class="label">Activity:</span>
      <span class="value">{{ sourceEntry.activity }}</span>
    </div>
    <div class="info-row">
      <span class="label">Time (mins):</span>
      <span class="value">{{ sourceEntry.timeInMins }}</span>
    </div>
  </div>

  <form [formGroup]="weekCopyForm" (ngSubmit)="onSubmit()">
    <div mat-dialog-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Week Starting</mat-label>
          <input matInput [matDatepicker]="startDatePicker" formControlName="startDate" required>
          <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #startDatePicker></mat-datepicker>
          <mat-error *ngIf="weekCopyForm.get('startDate')?.hasError('required')">
            Start date is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="weekdays-selection">
        <h3>Select Days to Copy To</h3>
        <div *ngIf="isLoading" class="loading-indicator">
          <mat-spinner diameter="30"></mat-spinner>
          <span>Checking available time for each day...</span>
        </div>
        <div class="weekday-list" *ngIf="!isLoading">
          <div *ngFor="let day of weekDays; let i = index"
               class="weekday-item"
               [class.selected]="day.selected"
               [class.disabled]="day.disabled"
               (click)="toggleDay(i)"
               [matTooltip]="day.disabledReason || ''"
               [matTooltipDisabled]="!day.disabled">
            <mat-checkbox [checked]="day.selected"
                         [disabled]="day.disabled"
                         (change)="toggleDay(i)"
                         (click)="$event.stopPropagation()">
              {{ formatDate(day.date) }}
              <span *ngIf="day.disabled && day.disabledReason === 'Source entry date'" class="source-indicator">(Source)</span>
              <span *ngIf="day.disabled && day.disabledReason !== 'Source entry date'" class="time-filled-indicator">(Time Filled)</span>
            </mat-checkbox>
          </div>
        </div>
      </div>
    </div>

    <div mat-dialog-actions align="end">
      <button mat-button type="button" (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" type="submit" [disabled]="weekCopyForm.invalid || !hasSelectedDays()">
        Copy to Selected Days
      </button>
    </div>
  </form>
</div>
